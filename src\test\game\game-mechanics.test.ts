import { describe, it, expect, beforeEach } from 'vitest';

describe('Game Mechanics Tests', () => {
  describe('Collision Detection Algorithms', () => {
    it('should detect AABB collision correctly', () => {
      const rectA = { x: 10, y: 10, width: 20, height: 20 };
      const rectB = { x: 15, y: 15, width: 20, height: 20 };
      
      const isColliding = (
        rectA.x < rectB.x + rectB.width &&
        rectA.x + rectA.width > rectB.x &&
        rectA.y < rectB.y + rectB.height &&
        rectA.y + rectA.height > rectB.y
      );
      
      expect(isColliding).toBe(true);
    });

    it('should detect no collision when rectangles are separate', () => {
      const rectA = { x: 10, y: 10, width: 20, height: 20 };
      const rectB = { x: 50, y: 50, width: 20, height: 20 };
      
      const isColliding = (
        rectA.x < rectB.x + rectB.width &&
        rectA.x + rectA.width > rectB.x &&
        rectA.y < rectB.y + rectB.height &&
        rectA.y + rectA.height > rectB.y
      );
      
      expect(isColliding).toBe(false);
    });

    it('should handle edge case collisions', () => {
      const rectA = { x: 10, y: 10, width: 20, height: 20 };
      const rectB = { x: 30, y: 10, width: 20, height: 20 }; // Touching edge
      
      const isColliding = (
        rectA.x < rectB.x + rectB.width &&
        rectA.x + rectA.width > rectB.x &&
        rectA.y < rectB.y + rectB.height &&
        rectA.y + rectA.height > rectB.y
      );
      
      expect(isColliding).toBe(false); // Touching but not overlapping
    });

    it('should handle circular collision detection', () => {
      const circleA = { x: 100, y: 100, radius: 25 };
      const circleB = { x: 120, y: 120, radius: 25 };
      
      const distance = Math.sqrt(
        Math.pow(circleB.x - circleA.x, 2) + 
        Math.pow(circleB.y - circleA.y, 2)
      );
      const isColliding = distance < (circleA.radius + circleB.radius);
      
      expect(isColliding).toBe(true);
    });
  });

  describe('Physics Calculations', () => {
    it('should calculate velocity with acceleration', () => {
      let velocity = { x: 0, y: 0 };
      const acceleration = { x: 2, y: -9.8 };
      const deltaTime = 0.016; // 60fps
      
      velocity.x += acceleration.x * deltaTime;
      velocity.y += acceleration.y * deltaTime;
      
      expect(velocity.x).toBeCloseTo(0.032, 3);
      expect(velocity.y).toBeCloseTo(-0.157, 3);
    });

    it('should calculate position with velocity', () => {
      let position = { x: 100, y: 200 };
      const velocity = { x: 50, y: -30 };
      const deltaTime = 0.016;
      
      position.x += velocity.x * deltaTime;
      position.y += velocity.y * deltaTime;
      
      expect(position.x).toBeCloseTo(100.8, 1);
      expect(position.y).toBeCloseTo(199.52, 2);
    });

    it('should apply friction to velocity', () => {
      let velocity = { x: 10, y: 0 };
      const friction = 0.9;
      
      velocity.x *= friction;
      
      expect(velocity.x).toBe(9);
    });

    it('should handle terminal velocity', () => {
      let velocity = { x: 0, y: -50 };
      const gravity = -9.8;
      const terminalVelocity = -20;
      const deltaTime = 0.016;
      
      velocity.y += gravity * deltaTime;
      if (velocity.y < terminalVelocity) {
        velocity.y = terminalVelocity;
      }
      
      expect(velocity.y).toBe(terminalVelocity);
    });
  });

  describe('Animation Systems', () => {
    it('should cycle through animation frames', () => {
      const animation = {
        frames: [0, 1, 2, 3],
        currentFrame: 0,
        frameTime: 0,
        frameDuration: 0.1
      };
      
      const deltaTime = 0.12;
      
      animation.frameTime += deltaTime;
      if (animation.frameTime >= animation.frameDuration) {
        animation.currentFrame = (animation.currentFrame + 1) % animation.frames.length;
        animation.frameTime = 0;
      }
      
      expect(animation.currentFrame).toBe(1);
      expect(animation.frameTime).toBe(0);
    });

    it('should handle animation speed scaling', () => {
      const animation = {
        frames: [0, 1, 2, 3],
        currentFrame: 0,
        frameTime: 0,
        frameDuration: 0.1,
        speed: 2.0 // 2x speed
      };
      
      const deltaTime = 0.06;
      
      animation.frameTime += deltaTime * animation.speed;
      if (animation.frameTime >= animation.frameDuration) {
        animation.currentFrame = (animation.currentFrame + 1) % animation.frames.length;
        animation.frameTime = 0;
      }
      
      expect(animation.currentFrame).toBe(1);
    });

    it('should handle animation looping', () => {
      const animation = {
        frames: [0, 1, 2],
        currentFrame: 2,
        frameTime: 0.1,
        frameDuration: 0.1
      };
      
      const deltaTime = 0.01;
      
      animation.frameTime += deltaTime;
      if (animation.frameTime >= animation.frameDuration) {
        animation.currentFrame = (animation.currentFrame + 1) % animation.frames.length;
        animation.frameTime = 0;
      }
      
      expect(animation.currentFrame).toBe(0); // Looped back to start
    });
  });

  describe('Particle System Mechanics', () => {
    it('should create particles with random properties', () => {
      const createParticle = (x: number, y: number) => ({
        x,
        y,
        vx: (Math.random() - 0.5) * 10,
        vy: (Math.random() - 0.5) * 10,
        life: 1.0,
        maxLife: 1.0,
        scale: Math.random() * 0.5 + 0.5,
        alpha: 1.0
      });
      
      const particle = createParticle(100, 200);
      
      expect(particle.x).toBe(100);
      expect(particle.y).toBe(200);
      expect(particle.life).toBe(1.0);
      expect(particle.scale).toBeGreaterThanOrEqual(0.5);
      expect(particle.scale).toBeLessThanOrEqual(1.0);
      expect(particle.vx).toBeGreaterThanOrEqual(-5);
      expect(particle.vx).toBeLessThanOrEqual(5);
    });

    it('should update particle properties over time', () => {
      const particle = {
        x: 100,
        y: 200,
        vx: 2,
        vy: -3,
        life: 1.0,
        maxLife: 1.0,
        scale: 1.0,
        alpha: 1.0
      };
      
      const deltaTime = 0.016;
      const gravity = 0.5;
      
      // Update particle
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.vy += gravity; // Apply gravity
      particle.life -= deltaTime;
      particle.alpha = particle.life / particle.maxLife;
      particle.scale = particle.alpha;
      
      expect(particle.x).toBe(102);
      expect(particle.y).toBe(197);
      expect(particle.vy).toBe(-2.5);
      expect(particle.life).toBeCloseTo(0.984, 3);
      expect(particle.alpha).toBeCloseTo(0.984, 3);
      expect(particle.scale).toBeCloseTo(0.984, 3);
    });

    it('should handle particle pooling for performance', () => {
      const particlePool: any[] = [];
      const activeParticles: any[] = [];
      
      // Create pool
      for (let i = 0; i < 100; i++) {
        particlePool.push({
          x: 0, y: 0, vx: 0, vy: 0,
          life: 0, maxLife: 1, active: false
        });
      }
      
      // Get particle from pool
      const getParticle = () => {
        for (const particle of particlePool) {
          if (!particle.active) {
            particle.active = true;
            return particle;
          }
        }
        return null;
      };
      
      // Return particle to pool
      const returnParticle = (particle: any) => {
        particle.active = false;
        const index = activeParticles.indexOf(particle);
        if (index > -1) {
          activeParticles.splice(index, 1);
        }
      };
      
      // Test pooling
      const particle1 = getParticle();
      const particle2 = getParticle();
      
      expect(particle1).not.toBeNull();
      expect(particle2).not.toBeNull();
      expect(particle1).not.toBe(particle2);
      
      activeParticles.push(particle1!, particle2!);
      
      returnParticle(particle1);
      const particle3 = getParticle();
      
      expect(particle3).toBe(particle1); // Reused from pool
    });
  });

  describe('Game State Management', () => {
    it('should handle game state transitions', () => {
      const gameState = {
        current: 'menu',
        previous: null,
        data: {}
      };
      
      const changeState = (newState: string, data = {}) => {
        gameState.previous = gameState.current;
        gameState.current = newState;
        gameState.data = data;
      };
      
      changeState('playing', { level: 1 });
      
      expect(gameState.current).toBe('playing');
      expect(gameState.previous).toBe('menu');
      expect(gameState.data).toEqual({ level: 1 });
    });

    it('should validate state transitions', () => {
      const validTransitions = {
        'menu': ['playing', 'settings'],
        'playing': ['paused', 'menu', 'game-over'],
        'paused': ['playing', 'menu'],
        'game-over': ['menu', 'playing']
      };
      
      const isValidTransition = (from: string, to: string) => {
        return validTransitions[from as keyof typeof validTransitions]?.includes(to) || false;
      };
      
      expect(isValidTransition('menu', 'playing')).toBe(true);
      expect(isValidTransition('playing', 'settings')).toBe(false);
      expect(isValidTransition('paused', 'game-over')).toBe(false);
    });
  });

  describe('Input Handling', () => {
    it('should handle key press and release', () => {
      const inputState = {
        left: false,
        right: false,
        jump: false,
        interact: false
      };
      
      const handleKeyDown = (key: string) => {
        switch (key) {
          case 'ArrowLeft':
          case 'a':
            inputState.left = true;
            break;
          case 'ArrowRight':
          case 'd':
            inputState.right = true;
            break;
          case ' ':
          case 'w':
            inputState.jump = true;
            break;
          case 'e':
            inputState.interact = true;
            break;
        }
      };
      
      const handleKeyUp = (key: string) => {
        switch (key) {
          case 'ArrowLeft':
          case 'a':
            inputState.left = false;
            break;
          case 'ArrowRight':
          case 'd':
            inputState.right = false;
            break;
          case ' ':
          case 'w':
            inputState.jump = false;
            break;
          case 'e':
            inputState.interact = false;
            break;
        }
      };
      
      handleKeyDown('ArrowLeft');
      handleKeyDown(' ');
      
      expect(inputState.left).toBe(true);
      expect(inputState.jump).toBe(true);
      expect(inputState.right).toBe(false);
      
      handleKeyUp('ArrowLeft');
      
      expect(inputState.left).toBe(false);
      expect(inputState.jump).toBe(true);
    });

    it('should handle input buffering', () => {
      const inputBuffer: string[] = [];
      const bufferTime = 0.1; // 100ms buffer
      
      const addToBuffer = (input: string) => {
        inputBuffer.push(input);
        setTimeout(() => {
          const index = inputBuffer.indexOf(input);
          if (index > -1) {
            inputBuffer.splice(index, 1);
          }
        }, bufferTime * 1000);
      };
      
      const consumeBuffer = (input: string) => {
        const index = inputBuffer.indexOf(input);
        if (index > -1) {
          inputBuffer.splice(index, 1);
          return true;
        }
        return false;
      };
      
      addToBuffer('jump');
      expect(inputBuffer).toContain('jump');
      
      const consumed = consumeBuffer('jump');
      expect(consumed).toBe(true);
      expect(inputBuffer).not.toContain('jump');
    });
  });
});
